# 📚 业务审批工作流接入完整指导文档

## 🎯 文档目标
本文档将指导开发者完成业务系统与审批工作流引擎的集成，包含详细的代码示例、流程图配置和最佳实践。

---

## 📋 目录
1. [前置准备](#前置准备)
2. [必须实现的组件](#必须实现的组件)
3. [流程图设计与配置](#流程图设计与配置)
4. [可选监听事件配置](#可选监听事件配置)
5. [测试验证](#测试验证)
6. [常见问题解决](#常见问题解决)

---

## 🔧 前置准备

### 1. 确认项目依赖
确保项目已包含以下模块：
- `beitai-extraordinary-flowable`（工作流引擎）
- `beitai-extraordinary-common`（公共工具）
- `beitai-extraordinary-notice`（通知服务）

### 2. 了解核心概念
- **BusinessKey**：业务键，格式为 `orderType_orderId_orderCode`
- **ProcessDefinitionKey**：流程定义键，用于标识不同的业务流程
- **approvalResult**：审批结果，`1`=通过，`!=1`=不通过
- **currentUserId**：当前用户ID，仅商家端提交时设置
- **templateId**：通知下一个审批节点的模板ID

---

## ⚠️ 重要设计原则

### 1. 工作流启动时机
- **save() 方法**：仅保存业务数据，不启动工作流
- **submit() 方法**：启动工作流或完成当前节点

### 2. currentUserId 设置规则
- **仅商家端**：商家提交流程时才设置 `currentUserId`
- **格式**：`"m:" + merchantId`
- **其他端**：管理端、系统端不设置此参数

### 3. templateId 作用机制
- **用途**：指定通知下一个审批节点的模板
- **使用位置**：在 NotificationTaskListener 中使用
- **设置时机**：在 buildVariables() 中设置

### 4. 模块解耦原则
- **业务处理器位置**：必须在 `biz` 模块下创建
- **解耦机制**：通过流程事件总线分发
- **职责分离**：流程模块组装流程数据，业务模块处理业务逻辑

---

## 🏗️ 必须实现的组件

### 步骤1：创建业务域实现类

```java
package com.beitai.extraordinary.biz.domain.impl;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class YourBusinessDomainImpl implements IYourBusinessDomain {

    @Resource
    IYourBusinessService yourBusinessService;
    @Resource
    ICodeRuleDomain codeRuleDomain;
    @Resource
    IFlowInstanceDomain flowInstanceDomain;
    @Resource
    IFlowTaskDomain flowTaskDomain;

    @Override
    public YourBusinessBO save(YourBusinessSaveDTO dto) {
        // 1. 转换并保存业务数据
        YourBusiness entity = DozerConverter.convert(dto, YourBusiness.class);
        entity.setCode(codeRuleDomain.getNumber(OrderType.YOUR_BUSINESS.getCode(), new HashMap<>()));
        entity.setStatus(YourBusinessEnums.Status.STATUS_0.getValue()); // 草稿状态
        
        yourBusinessService.save(entity);

        // 2. 保存业务数据（不启动工作流）
        // 注意：save方法通常只保存数据，工作流在submit方法中启动
        
        return DozerConverter.convert(entity, YourBusinessBO.class);
    }

    @Override
    public void submit(Long id) {
        YourBusiness entity = yourBusinessService.getById(id);
        entity.setStatus(YourBusinessEnums.Status.STATUS_1.getValue()); // 已提交状态

        // 启动工作流（在submit方法中启动）
        // 当前如果提交不作为一个节点在这里发起流程，否则保存的时候发起
        if (StringUtils.isBlank(entity.getProcessInstanceId())) {
            HashMap<String, Object> variables = buildVariables(entity);

            // 只有商家端提交时才设置currentUserId
            // variables.put("currentUserId", "m:" + merchantId); // 仅商家端需要

            // 设置审核人员（如果流程图中通过表达式定义了）
            variables.put("assignee", "审核人员ID");

            try {
                // 生成业务键
                String businessKey = BusinessKeyUtils.generateBusinessKey(
                    OrderType.YOUR_BUSINESS.getCode(),
                    entity.getId(),
                    entity.getCode()
                );

                // 启动流程实例
                String processInstanceId = flowInstanceDomain.startProcessInstanceByKey(
                    ProcessDefinitionKey.YOUR_PROCESS.getKey(),
                    variables,
                    businessKey
                );

                // 保存流程实例ID
                entity.setProcessInstanceId(processInstanceId);
                yourBusinessService.updateById(entity);

            } catch (Exception e) {
                log.error("启动工作流实例失败", e);
                throw new BusinessException("提交失败");
            }
        } else {
            // 当前如果提交不作为一个节点在这里删除，否则完成当前节点
            flowTaskDomain.completeNextTask(entity.getProcessInstanceId());
        }

        yourBusinessService.updateById(entity);
    }

    /**
     * 构建工作流变量 - 核心方法
     */
    public HashMap<String, Object> buildVariables(YourBusiness entity) {
        HashMap<String, Object> variables = new HashMap<>();
        variables.put("businessId", entity.getId());           // 业务ID，用于回调
        variables.put("businessCode", entity.getCode());       // 业务编码，用于显示
        variables.put("businessName", "您的业务名称");          // 业务名称，用于通知
        variables.put("templateId", 您的通知模板ID);            // 通知下一个审批节点的模板ID
        return variables;
    }
}
```

### 步骤2：创建工作流业务处理器

**重要说明**：工作流业务处理器必须在 `biz` 模块下创建，因为通过流程事件总线分发，实现了流程模块与业务模块的解耦。

```java
package com.beitai.extraordinary.biz.workflow;

@Component
@Order(您的优先级) // 数字越小优先级越高
@Slf4j
public class YourBusinessWorkflowHandler implements WorkflowBusinessHandler {

    @Resource
    private IYourBusinessService yourBusinessService;
    @Resource
    private INoticeLogDomain noticeLogDomain;

    @Override
    public boolean supports(WorkflowEvent event) {
        // 只处理您的业务流程的执行事件
        return event.getEventType() == WorkflowEventType.EXECUTION
               && ProcessDefinitionKey.YOUR_PROCESS.getKey().equals(event.getProcessDefinitionKey());
    }

    @Override
    public void handle(WorkflowEvent event) {
        // 只处理流程结束事件
        if (!"end".equals(event.getEventName())) {
            log.debug("忽略非流程结束事件: eventName={}", event.getEventName());
            return;
        }

        // 获取业务ID
        Long businessId = getBusinessIdFromVariables(event);
        if (businessId == null) {
            log.warn("无法从流程变量中获取业务ID");
            return;
        }

        log.info("处理业务流程结束事件: businessId={}", businessId);
        handleProcessEnd(businessId, event);
    }

    /**
     * 处理流程结束事件
     */
    private void handleProcessEnd(Long businessId, WorkflowEvent event) {
        try {
            // 获取最终审批结果
            Integer approvalResult = Integer.parseInt(
                event.getVariables().getOrDefault("approvalResult", "0").toString()
            );
            
            YourBusiness entity = yourBusinessService.getById(businessId);
            if (entity == null) {
                log.warn("业务数据不存在: businessId={}", businessId);
                return;
            }

            if (Objects.equals(approvalResult, WorkflowStatusEnums.Status.APPROVED.getValue())) {
                // 审核通过：只有在流程结束时才处理
                if (Boolean.TRUE.equals(event.getIsEnded())) {
                    entity.setStatus(YourBusinessEnums.Status.STATUS_2.getValue()); // 审核通过
                    yourBusinessService.updateById(entity);
                    
                    // 发送通过通知
                    sendNotification(entity, AuditRecordEnums.Result.RESULT_1);
                    
                    // 执行业务特定的后续处理
                    handleBusinessApproved(entity);
                    
                    log.info("流程通过且结束，已更新状态: businessId={}", businessId);
                }
            } else {
                // 审核不通过：立即处理
                entity.setStatus(YourBusinessEnums.Status.STATUS_3.getValue()); // 审核拒绝
                yourBusinessService.updateById(entity);
                
                // 发送拒绝通知
                sendNotification(entity, AuditRecordEnums.Result.RESULT_2);
                
                log.info("流程被拒绝，立即更新状态: businessId={}", businessId);
            }

        } catch (Exception e) {
            log.error("处理业务流程结束失败: businessId={}, error={}", businessId, e.getMessage(), e);
        }
    }

    /**
     * 业务审核通过后的特殊处理
     */
    private void handleBusinessApproved(YourBusiness entity) {
        // 在这里添加业务特定的处理逻辑
        // 例如：更新库存、发送邮件、调用第三方接口等
    }

    /**
     * 发送通知
     */
    private void sendNotification(YourBusiness entity, AuditRecordEnums.Result result) {
        noticeLogDomain.sendPush(
            NoticeLogEnums.UserType.STATUS_2, // 用户类型
            Lists.newArrayList(entity.getCreatorId()), // 接收人列表
            您的通知模板ID, // 通知模板ID
            OrderType.YOUR_BUSINESS, // 业务类型
            entity.getCode(), // 业务编码
            result, // 审核结果
            entity.getId() // 业务ID
        );
    }

    /**
     * 从流程变量中获取业务ID
     */
    private Long getBusinessIdFromVariables(WorkflowEvent event) {
        if (event.getVariables() == null) {
            return null;
        }

        try {
            Object businessId = event.getVariables().get("businessId");
            if (businessId instanceof Long) {
                return (Long) businessId;
            } else if (businessId instanceof Integer) {
                return ((Integer) businessId).longValue();
            } else if (businessId instanceof String) {
                return Long.valueOf((String) businessId);
            }
            return null;
        } catch (Exception e) {
            log.warn("解析业务ID失败: error={}", e.getMessage());
            return null;
        }
    }

    @Override
    public String getHandlerName() {
        return "yourBusinessWorkflowHandler";
    }
}
```

### 步骤3：添加枚举定义

```java
// 在 ProcessDefinitionKey 枚举中添加
public enum ProcessDefinitionKey {
    // ... 现有的枚举值
    YOUR_PROCESS("your_process_key", "您的业务审批流程");
}

// 在 OrderType 枚举中添加
public enum OrderType {
    // ... 现有的枚举值
    YOUR_BUSINESS("your_business", "您的业务类型");
}

// 创建业务状态枚举
public enum YourBusinessEnums {
    public enum Status {
        STATUS_0(0, "草稿"),
        STATUS_1(1, "已提交"),
        STATUS_2(2, "审核通过"),
        STATUS_3(3, "审核拒绝");
    }
}
```

---

## 🎨 流程图设计与配置

### 流程图基本结构

```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn">

  <process id="your_process_key" name="您的业务审批流程" isExecutable="true">

    <!-- 开始事件 -->
    <startEvent id="startEvent" name="开始"/>

    <!-- 商务审核节点 -->
    <userTask id="businessAudit" name="商务审核" flowable:candidateGroups="business_role">
      <extensionElements>
        <!-- 配置通知监听器 -->
        <flowable:taskListener event="create" delegateExpression="${notificationTaskListener}"/>
      </extensionElements>
    </userTask>

    <!-- 排他网关 - 商务审核结果判断 -->
    <exclusiveGateway id="businessGateway" name="商务审核结果"/>

    <!-- 财务审核节点 -->
    <userTask id="financeAudit" name="财务审核" flowable:candidateGroups="finance_role">
      <extensionElements>
        <!-- 配置通知监听器 -->
        <flowable:taskListener event="create" delegateExpression="${notificationTaskListener}"/>
      </extensionElements>
    </userTask>

    <!-- 排他网关 - 财务审核结果判断 -->
    <exclusiveGateway id="financeGateway" name="财务审核结果"/>

    <!-- 审核通过结束事件 -->
    <endEvent id="approvedEnd" name="审核通过"/>

    <!-- 审核拒绝结束事件 -->
    <endEvent id="rejectedEnd" name="审核拒绝"/>

    <!-- 流程连线 -->
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="businessAudit"/>

    <sequenceFlow id="flow2" sourceRef="businessAudit" targetRef="businessGateway"/>

    <!-- 商务审核通过 -->
    <sequenceFlow id="businessApproved" sourceRef="businessGateway" targetRef="financeAudit">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 1}</conditionExpression>
    </sequenceFlow>

    <!-- 商务审核拒绝 -->
    <sequenceFlow id="businessRejected" sourceRef="businessGateway" targetRef="rejectedEnd">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult != 1}</conditionExpression>
    </sequenceFlow>

    <sequenceFlow id="flow5" sourceRef="financeAudit" targetRef="financeGateway"/>

    <!-- 财务审核通过 -->
    <sequenceFlow id="financeApproved" sourceRef="financeGateway" targetRef="approvedEnd">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 1}</conditionExpression>
    </sequenceFlow>

    <!-- 财务审核拒绝 -->
    <sequenceFlow id="financeRejected" sourceRef="financeGateway" targetRef="rejectedEnd">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult != 1}</conditionExpression>
    </sequenceFlow>

  </process>

  <!-- 全局流程结束监听器 -->
  <process id="your_process_key">
    <extensionElements>
      <flowable:executionListener event="end" delegateExpression="${workflowEventBus}"/>
    </extensionElements>
  </process>

</definitions>
```

### 🔑 关键配置说明

#### 1. 审批结果条件表达式（统一规范）
```xml
<!-- 审核通过条件 -->
<conditionExpression xsi:type="tFormalExpression">${approvalResult == 1}</conditionExpression>

<!-- 审核不通过条件 -->
<conditionExpression xsi:type="tFormalExpression">${approvalResult != 1}</conditionExpression>
```

**重要说明**：
- ✅ **统一使用 `approvalResult`**：所有流程条件判断必须使用此变量
- ✅ **值为 1 表示通过**：`${approvalResult == 1}`
- ✅ **值不为 1 表示不通过**：`${approvalResult != 1}`
- ✅ **在 FlowTaskServiceImpl.complete() 中设置**：审核时会自动设置此变量

#### 2. 候选用户/候选组配置
```xml
<!-- 指定候选组 -->
<userTask id="businessAudit" flowable:candidateGroups="business_role,manager_role">

<!-- 指定候选用户 -->
<userTask id="financeAudit" flowable:candidateUsers="user1,user2">

<!-- 指定审核人（单人） -->
<userTask id="finalAudit" flowable:assignee="${assignee}">
```

---

## 🔔 可选监听事件配置

### 1. 通知监听器（推荐配置）

```xml
<userTask id="businessAudit" name="商务审核">
  <extensionElements>
    <!-- 任务创建时自动通知审核人 -->
    <flowable:taskListener event="create" delegateExpression="${notificationTaskListener}"/>
  </extensionElements>
</userTask>
```

**作用**：
- ✅ 自动设置任务优先级为 `0`
- ✅ 使用 `templateId` 参数选择通知模板
- ✅ 自动发送通知给下一个审批节点的审核人
- ✅ 触发工作流事件分发

**配置建议**：每个需要通知审核人的用户任务都应该配置

### 2. 全局流程监听器（必须配置）

```xml
<process id="your_process_key">
  <extensionElements>
    <!-- 流程结束时触发业务处理 -->
    <flowable:executionListener event="end" delegateExpression="${workflowEventBus}"/>
  </extensionElements>
</process>
```

**作用**：
- ✅ 流程结束时自动触发业务处理器
- ✅ 更新业务状态
- ✅ 发送最终通知

### 3. 自定义任务监听器（可选）

```xml
<userTask id="specialTask" name="特殊任务">
  <extensionElements>
    <!-- 任务完成时的自定义处理 -->
    <flowable:taskListener event="complete" delegateExpression="${yourCustomTaskListener}"/>

    <!-- 任务分配时的自定义处理 -->
    <flowable:taskListener event="assignment" delegateExpression="${yourAssignmentListener}"/>
  </extensionElements>
</userTask>
```

### 4. 网关监听器（可选）

```xml
<exclusiveGateway id="businessGateway">
  <extensionElements>
    <!-- 网关执行时的自定义逻辑 -->
    <flowable:executionListener event="start" delegateExpression="${yourGatewayListener}"/>
  </extensionElements>
</exclusiveGateway>
```

---

## 🧪 测试验证

### 步骤1：单元测试

```java
@SpringBootTest
@Transactional
class YourBusinessWorkflowTest {

    @Resource
    private IYourBusinessDomain yourBusinessDomain;

    @Resource
    private IFlowTaskService flowTaskService;

    @Test
    void testWorkflowIntegration() {
        // 1. 创建业务数据
        YourBusinessSaveDTO dto = new YourBusinessSaveDTO();
        // ... 设置测试数据

        // 2. 保存并启动工作流
        YourBusinessBO result = yourBusinessDomain.save(dto);
        assertNotNull(result.getId());

        // 3. 验证工作流是否启动
        // 查询待办任务
        FlowTodoQueryDto queryDto = new FlowTodoQueryDto();
        queryDto.setPriority(0);
        List<FlowTodoBo> todoList = flowTaskService.todoList(queryDto);

        // 验证是否有对应的待办任务
        assertTrue(todoList.stream().anyMatch(
            task -> task.getBusinessKey().contains(result.getId().toString())
        ));
    }

    @Test
    void testApprovalProcess() {
        // 1. 创建并提交业务
        // 2. 模拟审核通过
        // 3. 验证状态更新
        // 4. 验证通知发送
    }
}
```

### 步骤2：集成测试检查清单

#### ✅ 流程启动测试
- [ ] 业务保存后是否正确启动工作流
- [ ] BusinessKey 是否正确生成
- [ ] 流程变量是否正确设置

#### ✅ 审核流转测试
- [ ] 待办任务是否正确分配给审核人
- [ ] 审核通过后是否正确流转到下一节点
- [ ] 审核拒绝后是否正确结束流程

#### ✅ 业务状态测试
- [ ] 流程结束后业务状态是否正确更新
- [ ] 审核通过和拒绝的状态是否区分正确

#### ✅ 通知功能测试
- [ ] 任务创建时是否发送通知给审核人
- [ ] 流程结束时是否发送最终通知

---

## ❗ 常见问题解决

### 问题1：工作流启动失败

**现象**：调用 `startProcessInstanceByKey` 时抛出异常

**解决方案**：
```java
// 检查流程定义是否部署
ProcessDefinition definition = repositoryService.createProcessDefinitionQuery()
    .processDefinitionKey("your_process_key")
    .latestVersion()
    .singleResult();

if (definition == null) {
    throw new BusinessException("流程定义不存在，请检查BPMN文件是否正确部署");
}
```

### 问题2：待办任务查询不到

**现象**：流程启动了但查询不到待办任务

**排查步骤**：
1. 检查候选组/候选用户配置是否正确
2. 检查当前用户的角色权限
3. 检查优先级过滤条件

```java
// 调试用的查询语句
List<Task> allTasks = taskService.createTaskQuery()
    .processInstanceId("your_process_instance_id")
    .list();
```

### 问题3：审批结果条件不生效

**现象**：审核后流程不按预期流转

**检查要点**：
1. 确认条件表达式使用 `${approvalResult == 1}`
2. 确认 FlowTaskServiceImpl.complete() 正确设置了 approvalResult
3. 检查网关的默认流向配置

### 问题4：业务处理器不执行

**现象**：流程结束了但业务状态没有更新

**排查步骤**：
1. 检查 `supports()` 方法的流程键匹配
2. 检查全局监听器是否配置
3. 检查业务处理器的 `@Order` 注解

```java
@Override
public boolean supports(WorkflowEvent event) {
    log.info("检查支持: eventType={}, processKey={}",
        event.getEventType(), event.getProcessDefinitionKey());

    return event.getEventType() == WorkflowEventType.EXECUTION
           && ProcessDefinitionKey.YOUR_PROCESS.getKey().equals(event.getProcessDefinitionKey());
}
```

---

## 📝 最佳实践总结

### 1. 代码规范
- ✅ 统一使用 `approvalResult` 作为审批结果变量
- ✅ BusinessKey 格式：`orderType_orderId_orderCode`
- ✅ 流程定义键使用下划线命名：`your_business_audit`

### 2. 流程设计规范
- ✅ 每个用户任务配置通知监听器
- ✅ 全局配置流程结束监听器
- ✅ 网关条件使用统一的表达式格式

### 3. 错误处理
- ✅ 工作流启动失败时回滚业务数据
- ✅ 业务处理器中添加完善的异常处理
- ✅ 关键操作添加详细日志

### 4. 测试策略
- ✅ 编写完整的单元测试和集成测试
- ✅ 测试各种审批路径（通过/拒绝）
- ✅ 验证通知功能的正确性

---

## 🎉 完成检查清单

当您完成以下所有步骤后，业务审批工作流就成功接入了：

### 必须完成 ✅
- [ ] 创建业务域实现类（包含 save、submit、buildVariables 方法）
- [ ] 创建工作流业务处理器（处理流程结束事件）
- [ ] 添加流程定义和业务类型枚举
- [ ] 设计并部署 BPMN 流程图
- [ ] 配置全局流程结束监听器

### 推荐配置 🔧
- [ ] 在用户任务节点配置通知监听器
- [ ] 设置合适的候选组/候选用户
- [ ] 配置专用的通知模板

### 测试验证 🧪
- [ ] 验证工作流启动功能
- [ ] 测试审批流转逻辑
- [ ] 检查业务状态更新
- [ ] 确认通知发送功能

恭喜！您已经成功完成了业务审批工作流的接入。如果遇到问题，请参考常见问题解决部分，或查看现有的 SalesReceipts 实现作为参考。

---

**卢本伟牛逼** 🎊
